using System;

namespace DrMuscle.UITests.Helpers
{
    /// <summary>
    /// Configuration settings for UI tests
    /// </summary>
    public static class TestConfiguration
    {
        /// <summary>
        /// Default number of retry attempts for flaky tests
        /// </summary>
        public static int DefaultRetryCount => GetEnvironmentInt("UI_TEST_RETRY_COUNT", 3);
        

        /// <summary>
        /// Whether to capture screenshots on test failure
        /// </summary>
        public static bool CaptureScreenshotOnFailure => GetEnvironmentBool("UI_TEST_SCREENSHOT_ON_FAILURE", true);
        
        /// <summary>
        /// Timeout for waiting for UI elements - tuned for CI to reduce hangs
        /// </summary>
        public static TimeSpan DefaultTimeout => TimeSpan.FromSeconds(GetEnvironmentInt("UI_TEST_TIMEOUT_SECONDS", IsRunningInCI ? 30 : 15));

        /// <summary>
        /// Short timeout for quick checks (app launch, element presence)
        /// </summary>
        public static TimeSpan ShortTimeout => TimeSpan.FromSeconds(IsRunningInCI ? 15 : 8);

        /// <summary>
        /// App launch timeout - separate from element waits
        /// </summary>
        public static TimeSpan AppLaunchTimeout => TimeSpan.FromSeconds(IsRunningInCI ? 60 : 30);
        
        /// <summary>
        /// Whether we're running in CI/CD environment
        /// </summary>
        public static bool IsRunningInCI => GetEnvironmentBool("CI", false) || 
                                           GetEnvironmentBool("GITHUB_ACTIONS", false);
        
        /// <summary>
        /// App bundle identifier - matches MAUI project configuration
        /// </summary>
        public static string AppBundleId => Environment.GetEnvironmentVariable("APP_BUNDLE_ID") ?? "com.drmaxmuscle.dr_max_muscle";

        /// <summary>
        /// Retry count for flaky operations (disabled for fast feedback)
        /// </summary>
        public static int RetryCount => GetEnvironmentInt("UI_TEST_RETRY_COUNT", 1);

        /// <summary>
        /// Delay between retries in milliseconds (minimal for fast feedback)
        /// </summary>
        public static int RetryDelayMs => GetEnvironmentInt("UI_TEST_RETRY_DELAY_MS", 100);
        
        /// <summary>
        /// iOS app bundle path
        /// </summary>
        public static string? IosAppBundlePath => Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
        
        /// <summary>
        /// Whether to run tests in verbose mode
        /// </summary>
        public static bool VerboseLogging => GetEnvironmentBool("UI_TEST_VERBOSE", false);
        
        /// <summary>
        /// Whether to use fast mode timings (reduced sleeps)
        /// </summary>
        public static bool UseFastMode => GetEnvironmentBool("UI_TEST_FAST_MODE", false);
        
        private static int GetEnvironmentInt(string key, int defaultValue)
        {
            var value = Environment.GetEnvironmentVariable(key);
            return int.TryParse(value, out var result) ? result : defaultValue;
        }
        
        private static bool GetEnvironmentBool(string key, bool defaultValue)
        {
            var value = Environment.GetEnvironmentVariable(key);
            return bool.TryParse(value, out var result) ? result : defaultValue;
        }
        
        /// <summary>
        /// Log configuration settings
        /// </summary>
        public static void LogConfiguration()
        {
            Console.WriteLine("=== UI Test Configuration ===");
            Console.WriteLine($"Running in CI: {IsRunningInCI}");
            Console.WriteLine($"Default Retry Count: {DefaultRetryCount}");
            Console.WriteLine($"Retry Delay: {RetryDelayMs}ms");
            Console.WriteLine($"Default Timeout: {DefaultTimeout}");
            Console.WriteLine($"App Bundle ID: {AppBundleId}");
            Console.WriteLine($"iOS App Bundle Path: {IosAppBundlePath ?? "Not specified"}");
            Console.WriteLine($"Verbose Logging: {VerboseLogging}");
            Console.WriteLine($"Screenshot on Failure: {CaptureScreenshotOnFailure}");
            Console.WriteLine($"Fast Mode: {UseFastMode}");
            Console.WriteLine("===========================");
        }
    }
}
